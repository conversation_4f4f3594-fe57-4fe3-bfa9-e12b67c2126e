import {
    StyledModalActions,
    StyledModalContent,
    StyledModalHeader,
    StyledModalTitle,
    StyledSingleSelect,
    SuggestionProgressId
} from '@common';
import { PageContent } from '@components';
import { CustomTable } from '@components/reusableComponents/CustomTable/CustomTable';
import ListHeader from '@components/reusableComponents/ListHeader';
import TooltipDropdown from '@components/reusableComponents/TooltipDropdown';
import { OptionsType } from '@components/reusableComponents/TooltipDropdown/types';
import { Button, Tabs } from '@medly-components/core';
import { CallMadeIcon, CallReceivedIcon } from '@medly-components/icons';
import DOMPurify from 'dompurify';
import { ReceivedCols, SubmittedCols } from './columns';
import {
    CommentTextField,
    ProgressAndCommentWrapper,
    ProgressWrapper,
    StyledHeading,
    StyledHTMLText,
    StyledModalContentFlex,
    StyledPopup,
    StyledTableWrapper,
    StyledTabs,
    StyledText,
    SuggestionCommentContainer,
    SuggestionDescriptionLayer,
    SuggestionLayer
} from './Suggestions.styled';
import { useSuggestion } from './useSuggestion';

export const Suggestions = () => {
    const {
        modalState,
        suggestedDate,
        suggestedBy,
        suggestionText,
        isReceivedAllowed,
        categoryName,
        openModal,
        closeModal,
        addSuggestionPage,
        handleTabChange,
        suggestionsData,
        totalSuggestionsDataCount,
        handlePageChange,
        page,
        activeTab,
        suggestionsDataIsLoading,
        handleSortChange,
        tagsList,
        handleDropdownChange,
        progressStatus,
        handleProgressSubmission,
        progressLoading,
        suggestionReceivedFilter,
        suggestionFilterOptions,
        handleSuggestionReceievedDropdownChange,
        suggestionPendingCount,
        suggestionComment,
        setSuggestionComment,
        comments,
        removeHtmlTags
    } = useSuggestion();

    const MIN_COMMENT_LENGTH = 10;
    const MAX_COMMENT_LENGTH = 200;

    const rawSuggestionComment = removeHtmlTags(suggestionComment).trim();

    const isSaveDisabled =
        !progressStatus ||
        (progressStatus === SuggestionProgressId.Deferred && !rawSuggestionComment) ||
        (rawSuggestionComment.length > 0 && rawSuggestionComment.length < MIN_COMMENT_LENGTH) ||
        rawSuggestionComment.length > MAX_COMMENT_LENGTH;

    return (
        <PageContent>
            <ListHeader title="Suggestions" actionButtonLabel="Add Suggestion" actionButtonClick={addSuggestionPage} />

            <StyledPopup open={modalState} onCloseModal={closeModal}>
                <StyledModalHeader>
                    <StyledModalTitle textVariant="h2">{`Suggestion`}</StyledModalTitle>
                </StyledModalHeader>
                <StyledModalContent>
                    <StyledModalContentFlex>
                        <SuggestionLayer>
                            <StyledHeading textWeight="Medium">
                                Date: <StyledText>{suggestedDate}</StyledText>
                            </StyledHeading>
                        </SuggestionLayer>
                        <SuggestionLayer>
                            <StyledHeading textVariant="h4" textWeight="Medium">
                                Category: <StyledText>{categoryName}</StyledText>
                            </StyledHeading>
                        </SuggestionLayer>
                        <SuggestionLayer>
                            <StyledHeading textVariant="h4" textWeight="Medium">
                                Suggested By: <StyledText>{suggestedBy}</StyledText>
                            </StyledHeading>
                        </SuggestionLayer>
                        <SuggestionDescriptionLayer>
                            <StyledHeading textVariant="h4" textWeight="Medium">
                                Suggestion:
                            </StyledHeading>
                            <StyledHTMLText dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(suggestionText) }} />
                        </SuggestionDescriptionLayer>
                        {activeTab === 'receivedSuggestion' && (
                            <ProgressAndCommentWrapper>
                                <ProgressWrapper>
                                    <StyledSingleSelect
                                        options={tagsList}
                                        variant="outlined"
                                        placeholder="Select Progress"
                                        size="M"
                                        label="Progress"
                                        value={progressStatus}
                                        isSearchable
                                        minWidth="100%"
                                        onChange={val => val && handleDropdownChange(val)}
                                        data-testid="progress"
                                    />
                                    <TooltipDropdown
                                        dataIds={[`progress-input`]}
                                        values={
                                            progressStatus && tagsList.length
                                                ? tagsList.filter((item: OptionsType) => progressStatus === item.value)
                                                : []
                                        }
                                    />
                                </ProgressWrapper>
                                <CommentTextField
                                    label="Comment"
                                    placeholder="Please add comment here"
                                    variant="outlined"
                                    value={suggestionComment}
                                    minLength={MIN_COMMENT_LENGTH}
                                    maxLength={MAX_COMMENT_LENGTH}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSuggestionComment(e.target.value)}
                                    errorText={
                                        rawSuggestionComment.length > 0 && rawSuggestionComment.length < MIN_COMMENT_LENGTH
                                            ? `Comment must be between ${MIN_COMMENT_LENGTH} and ${MAX_COMMENT_LENGTH} characters.`
                                            : ''
                                    }
                                />
                            </ProgressAndCommentWrapper>
                        )}
                        {comments.length > 0 && (
                            <SuggestionDescriptionLayer>
                                {comments.map(c => (
                                    <SuggestionCommentContainer key={c.id}>
                                        <StyledHeading textVariant="h4" textWeight="Medium">
                                            Comment: <StyledText>{c.date}</StyledText>
                                        </StyledHeading>
                                        <StyledHTMLText dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(c.comment) }} />
                                    </SuggestionCommentContainer>
                                ))}
                            </SuggestionDescriptionLayer>
                        )}
                    </StyledModalContentFlex>
                </StyledModalContent>
                {activeTab === 'receivedSuggestion' && (
                    <StyledModalActions>
                        <Button isLoading={progressLoading} disabled={isSaveDisabled} onClick={handleProgressSubmission}>
                            Save
                        </Button>
                    </StyledModalActions>
                )}
            </StyledPopup>
            <StyledSingleSelect
                options={suggestionFilterOptions}
                variant="outlined"
                placeholder="Select Progress"
                data-testid="progressDropdown"
                label={'Progress'}
                onChange={e => handleSuggestionReceievedDropdownChange(e)}
                value={suggestionReceivedFilter.progressId}
                size="M"
                minWidth="25rem"
            />
            <StyledSingleSelect
                options={suggestionFilterOptions}
                variant="outlined"
                placeholder="Select Category"
                data-testid="progressDropdown"
                label={'Progress'}
                onChange={e => handleSuggestionReceievedDropdownChange(e)}
                value={suggestionReceivedFilter.progressId}
                size="M"
                minWidth="25rem"
            />
            <StyledTabs aria-label="Closed style tabs" tabSize="M" variant="outlined" onChange={id => handleTabChange(id)}>
                <Tabs.Tab
                    active={activeTab === 'submittedSuggestion'}
                    id="submittedSuggestion"
                    label="Submitted"
                    helperText="Submitted"
                    icon={CallMadeIcon}
                    data-testid="submitted"
                />
                <Tabs.Tab
                    active={activeTab === 'receivedSuggestion'}
                    id="receivedSuggestion"
                    label="Received"
                    helperText="Received"
                    icon={CallReceivedIcon}
                    count={suggestionPendingCount}
                    hide={!isReceivedAllowed}
                    data-testid="received"
                />
            </StyledTabs>
            <StyledTableWrapper>
                <CustomTable
                    data={suggestionsData}
                    tableKey={activeTab}
                    columns={activeTab === 'submittedSuggestion' ? SubmittedCols : ReceivedCols}
                    isLoading={suggestionsDataIsLoading}
                    activePage={page || 1}
                    count={totalSuggestionsDataCount || 0}
                    handlePageChange={handlePageChange}
                    setSortOrder={handleSortChange}
                />
            </StyledTableWrapper>
        </PageContent>
    );
};
